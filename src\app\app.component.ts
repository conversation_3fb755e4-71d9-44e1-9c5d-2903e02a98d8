import { Component } from '@angular/core';
import { ColDef } from 'ag-grid-community';
import { CardRendererComponent } from './card-renderer.component';
import { BreakpointObserver } from '@angular/cdk/layout';

interface RowData {
  id: number;
  name: string;
  email: string;
  phone: string;
  city: string;
}

interface FieldConfig {
  label: string;
  field: string;
}

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  standalone: false,
  styleUrl: './app.component.css',
})
export class AppComponent {
  title = 'aggrid-responsive';

  isMobile = false;

  standardColumnDefs = [
    { headerName: 'ID', field: 'id', minWidth: 80 },
    { headerName: 'Name', field: 'name', minWidth: 150 },
    { headerName: 'Email', field: 'email', minWidth: 200 },
    { headerName: 'Phone', field: 'phone', minWidth: 150 },
    { headerName: 'City', field: 'city', minWidth: 120 },
  ];

  cardColumnDefs = [
    {
      headerName: 'Card View',
      field: 'card',
      cellRenderer: CardRendererComponent,
      autoHeight: true,
      flex: 1,
      cellRendererParams: {
        fields: this.standardColumnDefs.map((col): FieldConfig => ({
          label: col.headerName,
          field: col.field
        }))
      },
    },
  ];

  columnDefs: ColDef[] = this.standardColumnDefs;

  rowData: RowData[] = Array.from({ length: 10 }, (_, i) => ({
    id: i + 1,
    name: `User ${i + 1}`,
    email: `user${i + 1}@example.com`,
    phone: `+12345678${i + 1}`,
    city: ['Berlin', 'Paris', 'Madrid', 'Rome', 'Vienna'][i % 5],
  }));

  constructor(private breakpointObserver: BreakpointObserver) {
    this.breakpointObserver
      .observe(['(max-width: 600px)'])
      .subscribe((result) => {
        this.isMobile = result.matches;
        this.columnDefs = this.isMobile
          ? this.cardColumnDefs
          : this.standardColumnDefs;
      });
  }
}
