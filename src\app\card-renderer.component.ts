import { Component } from '@angular/core';
import { ICellRendererAngularComp } from 'ag-grid-angular';

@Component({
  selector: 'app-card-renderer',
  standalone: false,
  template: `
    <div class="card">
      <div *ngFor="let field of fields">
        <strong>{{ field.label }}:</strong> {{ params.data[field.field] }}
      </div>
    </div>
  `,
})
export class CardRendererComponent implements ICellRendererAngularComp {
  params: any;
  fields: { label: string; field: string }[] = [];

  agInit(params: any): void {
    this.params = params;
    this.fields =
      params?.fields || params?.colDef?.cellRendererParams?.fields || [];
  }

  refresh(): boolean {
    return false;
  }
}
