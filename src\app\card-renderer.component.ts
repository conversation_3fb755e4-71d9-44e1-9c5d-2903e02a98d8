import { Component } from '@angular/core';
import { ICellRendererAngularComp } from 'ag-grid-angular';
import { FieldConfig, CardRendererParams } from './types/grid.types';

/**
 * Reusable card renderer component for AG Grid
 *
 * Usage:
 * - Import FieldConfig and CardRendererParams from './types/grid.types'
 * - Pass fields configuration via cellRendererParams: { fields: FieldConfig[] }
 * - Each field should have a label (display name) and field (data property name)
 */

@Component({
  selector: 'app-card-renderer',
  standalone: false,
  template: `
    <div class="card">
      <div *ngFor="let field of fields">
        <strong>{{ field.label }}:</strong> {{ getFieldValue(field.field) }}
      </div>
    </div>
  `,
})
export class CardRendererComponent implements ICellRendererAngularComp {
  params!: CardRendererParams;
  fields: FieldConfig[] = [];

  agInit(params: CardRendererParams): void {
    this.params = params;
    this.fields = params.colDef?.cellRendererParams?.fields || [];
  }

  getFieldValue(field: string): unknown {
    return this.params.data?.[field];
  }

  refresh(): boolean {
    return false;
  }
}
