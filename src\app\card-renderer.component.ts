import { Component } from '@angular/core';
import { ICellRendererAngularComp } from 'ag-grid-angular';
import { ICellRendererParams } from 'ag-grid-community';

interface FieldConfig {
  label: string;
  field: string;
}

interface CardRendererParams extends ICellRendererParams {
  fields: FieldConfig[];
}

interface RowData {
  id: number;
  name: string;
  email: string;
  phone: string;
  city: string;
}

@Component({
  selector: 'app-card-renderer',
  standalone: false,
  template: `
    <div class="card">
      <div *ngFor="let field of fields">
        <strong>{{ field.label }}:</strong> {{ getFieldValue(field.field) }}
      </div>
    </div>
  `,
})
export class CardRendererComponent implements ICellRendererAngularComp {
  params!: CardRendererParams;
  fields: FieldConfig[] = [];

  agInit(params: CardRendererParams): void {
    this.params = params;
    this.fields = params.colDef?.cellRendererParams?.fields || [];
  }

  getFieldValue(field: string): string | number {
    const data = this.params.data as RowData;
    return data[field as keyof RowData];
  }

  refresh(): boolean {
    return false;
  }
}
