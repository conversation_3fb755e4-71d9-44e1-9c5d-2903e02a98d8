import { ICellRendererParams } from 'ag-grid-community';

export interface FieldConfig {
  label: string;
  field: string;
}

// Generic interface for any row data type
export interface BaseRowData {
  [key: string]: any;
}

// Specific implementation for this application
export interface RowData extends BaseRowData {
  id: number;
  name: string;
  email: string;
  phone: string;
  city: string;
}

export interface CardRendererParams<T = BaseRowData> extends ICellRendererParams<T> {
  fields: FieldConfig[];
}
